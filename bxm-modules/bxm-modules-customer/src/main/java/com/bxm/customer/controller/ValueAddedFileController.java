package com.bxm.customer.controller;

import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.core.domain.Result;
import com.bxm.common.log.annotation.Log;
import com.bxm.common.log.enums.BusinessType;
import com.bxm.customer.service.IValueAddedFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 增值交付单文件Controller
 *
 * 提供增值交付单相关文件的上传和管理功能
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Slf4j
@RestController
@RequestMapping("/valueAddedFile")
@Api(tags = "增值交付单文件管理")
public class ValueAddedFileController extends BaseController {

    @Autowired
    private IValueAddedFileService valueAddedFileService;

    /**
     * 上传交付单文件
     */
    @PostMapping("/uploadDeliveryOrderFile")
    @ApiOperation(value = "上传交付单文件", notes = "上传与指定交付单编号关联的文件")
    @Log(title = "上传交付单文件", businessType = BusinessType.INSERT)
    public Result<Long> uploadDeliveryOrderFile(
            @RequestParam("deliveryOrderNo") @ApiParam(value = "交付单编号", required = true) String deliveryOrderNo,
            @RequestParam("file") @ApiParam(value = "要上传的文件", required = true) MultipartFile file) {
        try {
            log.info("Received upload delivery order file request, deliveryOrderNo: {}, fileName: {}", 
                    deliveryOrderNo, file != null ? file.getOriginalFilename() : "null");

            // 基础参数验证
            if (deliveryOrderNo == null || deliveryOrderNo.trim().isEmpty()) {
                return Result.fail("交付单编号不能为空");
            }
            if (file == null || file.isEmpty()) {
                return Result.fail("文件不能为空");
            }

            // 调用服务层保存文件
            Long fileId = valueAddedFileService.saveDeliveryOrderFile(file, deliveryOrderNo);

            log.info("Upload delivery order file success, fileId: {}, deliveryOrderNo: {}",
                    fileId, deliveryOrderNo);
            return Result.ok(fileId, "文件上传成功");

        } catch (IllegalArgumentException e) {
            log.warn("Parameter validation error in upload delivery order file: {}", e.getMessage());
            return Result.fail("参数验证失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error in upload delivery order file, deliveryOrderNo: {}",
                    deliveryOrderNo, e);
            return Result.fail("文件上传失败：" + e.getMessage());
        }
    }

    /**
     * 删除文件
     */
    @DeleteMapping("/del")
    @ApiOperation(value = "删除文件", notes = "软删除指定的文件")
    @Log(title = "删除文件", businessType = BusinessType.DELETE)
    public Result<Void> del(
            @RequestParam("fileId") @ApiParam(value = "文件ID", required = true) Long fileId,
            @RequestParam("deliveryOrderNo") @ApiParam(value = "交付单编号", required = true) String deliveryOrderNo) {
        try {
            log.info("Received delete file request, fileId: {}, deliveryOrderNo: {}", fileId, deliveryOrderNo);

            // 基础参数验证
            if (fileId == null) {
                return Result.fail("文件ID不能为空");
            }
            if (deliveryOrderNo == null || deliveryOrderNo.trim().isEmpty()) {
                return Result.fail("交付单编号不能为空");
            }

            // 调用服务层删除文件
            boolean result = valueAddedFileService.deleteFile(fileId, deliveryOrderNo);

            if (result) {
                log.info("Delete file success, fileId: {}, deliveryOrderNo: {}", fileId, deliveryOrderNo);
                return Result.ok(null, "文件删除成功");
            } else {
                return Result.fail("文件删除失败");
            }

        } catch (IllegalArgumentException e) {
            log.warn("Parameter validation error in delete file: {}", e.getMessage());
            return Result.fail("参数验证失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error in delete file, fileId: {}, deliveryOrderNo: {}",
                    fileId, deliveryOrderNo, e);
            return Result.fail("文件删除失败：" + e.getMessage());
        }
    }

    /**
     * 获取文件下载链接
     */
    @GetMapping("/download")
    @ApiOperation(value = "获取文件下载链接", notes = "获取指定文件的下载URL")
    public Result<String> download(
            @RequestParam("fileId") @ApiParam(value = "文件ID", required = true) Long fileId,
            @RequestParam("deliveryOrderNo") @ApiParam(value = "交付单编号", required = true) String deliveryOrderNo) {
        try {
            log.info("Received get download URL request, fileId: {}, deliveryOrderNo: {}", fileId, deliveryOrderNo);

            // 基础参数验证
            if (fileId == null) {
                return Result.fail("文件ID不能为空");
            }
            if (deliveryOrderNo == null || deliveryOrderNo.trim().isEmpty()) {
                return Result.fail("交付单编号不能为空");
            }

            // 调用服务层获取下载URL
            String downloadUrl = valueAddedFileService.getFileDownloadUrl(fileId, deliveryOrderNo);

            log.info("Get download URL success, fileId: {}, deliveryOrderNo: {}", fileId, deliveryOrderNo);
            return Result.ok(downloadUrl, "获取下载链接成功");

        } catch (IllegalArgumentException e) {
            log.warn("Parameter validation error in get download URL: {}", e.getMessage());
            return Result.fail("参数验证失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error in get download URL, fileId: {}, deliveryOrderNo: {}",
                    fileId, deliveryOrderNo, e);
            return Result.fail("获取下载链接失败：" + e.getMessage());
        }
    }
}

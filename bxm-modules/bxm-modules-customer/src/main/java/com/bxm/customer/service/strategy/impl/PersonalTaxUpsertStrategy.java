package com.bxm.customer.service.strategy.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.ValueAddedEmployee;
import com.bxm.customer.domain.enums.ValueAddedBizType;
import com.bxm.customer.domain.enums.ValueAddedOperationType;
import com.bxm.customer.mapper.ValueAddedEmployeeMapper;
import com.bxm.customer.service.strategy.AbstractValueAddedEmployeeUpsertStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 个税明细业务类型的upsert策略实现
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Component
public class PersonalTaxUpsertStrategy extends AbstractValueAddedEmployeeUpsertStrategy {

    @Autowired
    private ValueAddedEmployeeMapper valueAddedEmployeeMapper;

    @Override
    public Integer getSupportedBizType() {
        return ValueAddedBizType.PERSONAL_TAX.getCode();
    }

    @Override
    public void validateBusinessFields(ValueAddedEmployee employee) {
        // 只验证核心业务逻辑，详细格式验证已通过@Valid注解处理
        // 验证操作类型是否适用于个税明细业务
        if (!ValueAddedOperationType.isValidForBizType(ValueAddedBizType.PERSONAL_TAX, employee.getOperationType())) {
            throw new IllegalArgumentException("Invalid operation type for personal tax business: " + employee.getOperationType());
        }

        // 验证应发工资（个税明细业务必须有工资信息）
        if (employee.getGrossSalary() == null || employee.getGrossSalary().doubleValue() <= 0) {
            throw new IllegalArgumentException("Gross salary is required and must be positive for personal tax");
        }


    }

    @Override
    public void preprocessEmployee(ValueAddedEmployee employee) {
        // 设置业务类型
        employee.setBizType(ValueAddedBizType.PERSONAL_TAX.getCode());

        // 清理其他业务类型专用字段
        employee.setTaxNumber(null);
        employee.setQueryPassword(null);
        // 个税明细业务保留 socialInsurance 字段，用于存储社保信息

        // 标准化身份证号（去除空格，转大写）
        if (StringUtils.isNotEmpty(employee.getIdNumber())) {
            employee.setIdNumber(employee.getIdNumber().trim().toUpperCase());
        }


    }

    @Override
    public void postprocessEmployee(ValueAddedEmployee employee, boolean isUpdate) {
        String operation = isUpdate ? "updated" : "created";
        log.info("Personal tax employee {} successfully: ID={}, Name={}, Operation={}, Salary={}", operation, employee.getId(), employee.getEmployeeName(), employee.getOperationType(), employee.getGrossSalary());

        // 个税明细业务的后处理逻辑
        // 例如：触发个税计算、发送通知等
    }

    @Override
    public ValueAddedEmployee findExistingEmployee(ValueAddedEmployee employee) {
        // 个税明细业务的唯一性判断：交付单编号 + 身份证号 + 业务类型
        LambdaQueryWrapper<ValueAddedEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, employee.getDeliveryOrderNo())
                .eq(ValueAddedEmployee::getIdNumber, employee.getIdNumber())
                .eq(ValueAddedEmployee::getBizType, ValueAddedBizType.PERSONAL_TAX.getCode());

        return valueAddedEmployeeMapper.selectOne(queryWrapper);
    }

    @Override
    public ValueAddedEmployee mergeEmployee(ValueAddedEmployee existing, ValueAddedEmployee newEmployee) {
        // 更新基础信息
        existing.setEmployeeName(newEmployee.getEmployeeName());
        existing.setMobile(newEmployee.getMobile());
        existing.setOperationType(newEmployee.getOperationType());
        existing.setEntryType(newEmployee.getEntryType());

        // 更新个税明细特定字段
        if (newEmployee.getGrossSalary() != null) {
            existing.setGrossSalary(newEmployee.getGrossSalary());
        }
        if (newEmployee.getProvidentFundPersonal() != null) {
            existing.setProvidentFundPersonal(newEmployee.getProvidentFundPersonal());
        }
        if (newEmployee.getSocialInsurance() != null) {
            existing.setSocialInsurance(newEmployee.getSocialInsurance());
        }
        if (StringUtils.isNotEmpty(newEmployee.getExtendInfo())) {
            existing.setExtendInfo(newEmployee.getExtendInfo());
        }
        if (StringUtils.isNotEmpty(newEmployee.getRemark())) {
            existing.setRemark(newEmployee.getRemark());
        }
        if (newEmployee.getStatus() != null) {
            existing.setStatus(newEmployee.getStatus());
        }

        return existing;
    }


}

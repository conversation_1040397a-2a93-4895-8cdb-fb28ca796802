package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.CustomerServicePeriodMonthServiceType;
import com.bxm.common.core.enums.DeliverType;
import com.bxm.common.core.enums.ServiceStatus;
import com.bxm.common.core.enums.TagBusinessType;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierDeliverStatus;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierType;
import com.bxm.common.core.enums.accountingCashier.BankPaymentResult;
import com.bxm.common.core.enums.inAccount.InAccountStatus;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.api.domain.dto.RemoteCustomerPeriodDTO;
import com.bxm.customer.api.domain.vo.RemoteCustomerServiceIncomeSearchVO;
import com.bxm.customer.domain.*;
import com.bxm.customer.domain.dto.TagDTO;
import com.bxm.customer.domain.vo.CustomerPeriodVO;
import com.bxm.customer.domain.vo.CustomerServiceChangeBusinessDeptVO;
import com.bxm.customer.domain.vo.CustomerServiceDispatchVO;
import com.bxm.customer.domain.vo.CustomerServiceTaxTypeCheckVO;
import com.bxm.customer.mapper.CCustomerServiceMapper;
import com.bxm.customer.mapper.CustomerDeliverMapper;
import com.bxm.customer.mapper.CustomerServiceBankAccountMapper;
import com.bxm.customer.mapper.CustomerServicePeriodMonthMapper;
import com.bxm.customer.properties.SpecialTagProperties;
import com.bxm.customer.service.*;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.RemoteEmployeeService;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.api.domain.SysEmployee;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 客户服务月度账期Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-08
 */
@Service
@Slf4j
public class CustomerServicePeriodMonthServiceImpl extends ServiceImpl<CustomerServicePeriodMonthMapper, CustomerServicePeriodMonth> implements ICustomerServicePeriodMonthService {
    @Autowired
    private CustomerServicePeriodMonthMapper customerServicePeriodMonthMapper;

    @Autowired
    private ICCustomerServicePeriodEmployeeService customerServicePeriodEmployeeService;

    @Autowired
    private ICBusinessTagRelationService businessTagRelationService;

    @Autowired
    private ICustomerServicePeriodMonthTaxTypeCheckService customerServicePeriodMonthTaxTypeCheckService;

    @Autowired
    private ICustomerServicePeriodMonthIncomeService customerServicePeriodMonthIncomeService;

    @Autowired
    @Lazy
    private ICustomerServiceInAccountService customerServiceInAccountService;

    @Autowired
    private CCustomerServiceMapper customerServiceMapper;

    @Autowired
    private RemoteEmployeeService remoteEmployeeService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private ICustomerTaxTypeCheckService customerTaxTypeCheckService;

    @Autowired
    private ICustomerServicePeriodYearService customerServicePeriodYearService;

    @Autowired
    private CustomerDeliverMapper customerDeliverMapper;

    @Autowired
    private SpecialTagProperties specialTagProperties;

    @Autowired
    private CustomerServiceBankAccountMapper customerServiceBankAccountMapper;

    @Autowired
    @Lazy
    private ICustomerServiceBankAccountService customerServiceBankAccountService;

    @Autowired
    private IMaterialDeliverFileInventoryService materialDeliverFileInventoryService;

    @Autowired
    @Lazy
    private ICustomerServiceCashierAccountingService customerServiceCashierAccountingService;

    /**
     * 查询客户服务月度账期
     *
     * @param id 客户服务月度账期主键
     * @return 客户服务月度账期
     */
    @Override
    public CustomerServicePeriodMonth selectCustomerServicePeriodMonthById(Long id) {
        return customerServicePeriodMonthMapper.selectCustomerServicePeriodMonthById(id);
    }

    /**
     * 查询客户服务月度账期列表
     *
     * @param customerServicePeriodMonth 客户服务月度账期
     * @return 客户服务月度账期
     */
    @Override
    public List<CustomerServicePeriodMonth> selectCustomerServicePeriodMonthList(CustomerServicePeriodMonth customerServicePeriodMonth) {
        return customerServicePeriodMonthMapper.selectCustomerServicePeriodMonthList(customerServicePeriodMonth);
    }

    /**
     * 新增客户服务月度账期
     *
     * @param customerServicePeriodMonth 客户服务月度账期
     * @return 结果
     */
    @Override
    public int insertCustomerServicePeriodMonth(CustomerServicePeriodMonth customerServicePeriodMonth) {
        customerServicePeriodMonth.setCreateTime(DateUtils.getNowDate());
        return customerServicePeriodMonthMapper.insertCustomerServicePeriodMonth(customerServicePeriodMonth);
    }

    /**
     * 修改客户服务月度账期
     *
     * @param customerServicePeriodMonth 客户服务月度账期
     * @return 结果
     */
    @Override
    public int updateCustomerServicePeriodMonth(CustomerServicePeriodMonth customerServicePeriodMonth) {
        customerServicePeriodMonth.setUpdateTime(DateUtils.getNowDate());
        return customerServicePeriodMonthMapper.updateCustomerServicePeriodMonth(customerServicePeriodMonth);
    }

    /**
     * 批量删除客户服务月度账期
     *
     * @param ids 需要删除的客户服务月度账期主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServicePeriodMonthByIds(Long[] ids) {
        return customerServicePeriodMonthMapper.deleteCustomerServicePeriodMonthByIds(ids);
    }

    /**
     * 删除客户服务月度账期信息
     *
     * @param id 客户服务月度账期主键
     * @return 结果
     */
    @Override
    public int deleteCustomerServicePeriodMonthById(Long id) {
        return customerServicePeriodMonthMapper.deleteCustomerServicePeriodMonthById(id);
    }

    @Override
    @Async
    public void saveMonthPeriodList(CCustomerService customerService, List<CustomerServicePeriodMonth> monthPeriodList, List<TagDTO> periodTags, List<CustomerServiceTaxTypeCheckVO> taxTypeCheckList, Long deptId, Long userId, String operName) {
        if (ObjectUtils.isEmpty(monthPeriodList)) {
            return;
        }
        List<SysEmployee> advisorEmployees = Objects.isNull(monthPeriodList.get(0).getAdvisorDeptId()) ? Lists.newArrayList() :
                remoteEmployeeService.getEmployeeListByDeptId(monthPeriodList.get(0).getAdvisorDeptId()).getDataThrowException();
        List<SysEmployee> accountingEmployees = Objects.isNull(monthPeriodList.get(0).getAccountingDeptId()) ? Lists.newArrayList() :
                remoteEmployeeService.getEmployeeListByDeptId(monthPeriodList.get(0).getAccountingDeptId()).getDataThrowException();
        Integer nowPeriodInt = DateUtils.getNowPeriod();
        boolean hasTicketTag = !ObjectUtils.isEmpty(periodTags) && periodTags.stream().anyMatch(row -> Objects.equals(row.getId(), specialTagProperties.getPprz()));
        monthPeriodList.forEach(monthPeriod -> {
            save(monthPeriod);
            if (!ObjectUtils.isEmpty(advisorEmployees)) {
                customerServicePeriodEmployeeService.saveBatch(advisorEmployees.stream().map(advisorEmployee ->
                        new CCustomerServicePeriodEmployee().setPeriodId(monthPeriod.getId())
                                .setPeriodEmployeeType(1)
                                .setEmployeeId(advisorEmployee.getEmployeeId())
                                .setEmployeeName(advisorEmployee.getEmployeeName())
                ).collect(Collectors.toList()));
            }
            if (!ObjectUtils.isEmpty(accountingEmployees)) {
                customerServicePeriodEmployeeService.saveBatch(accountingEmployees.stream().map(accoutingEmployee ->
                        new CCustomerServicePeriodEmployee().setPeriodId(monthPeriod.getId())
                                .setPeriodEmployeeType(2)
                                .setEmployeeId(accoutingEmployee.getEmployeeId())
                                .setEmployeeName(accoutingEmployee.getEmployeeName())
                ).collect(Collectors.toList()));
            }
            if (!ObjectUtils.isEmpty(periodTags)) {
                businessTagRelationService.saveByBusinessIdAndBusinessType(monthPeriod.getId(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD, periodTags);
            }
            if (!ObjectUtils.isEmpty(taxTypeCheckList)) {
                customerServicePeriodMonthTaxTypeCheckService.saveByCheckVOList(monthPeriod.getId(), taxTypeCheckList);
            }
            if (monthPeriod.getPeriod() < nowPeriodInt && !hasTicketTag) {
                customerServiceCashierAccountingService.addInAccountFromPeriod(customerService, monthPeriod, deptId, userId, operName);
            }
        });
        Map<Integer, CustomerServicePeriodMonthIncome> incomeMap = customerServicePeriodMonthIncomeService.selectByCustomerServiceId(customerService.getId()).stream().collect(Collectors.toMap(CustomerServicePeriodMonthIncome::getPeriod, Function.identity(), (v1, v2) -> v1));
        List<CustomerServicePeriodMonthIncome> newIncomes = Lists.newArrayList();
        LocalDate nowPeriod = LocalDate.parse(DateUtils.getNowPeriod() + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
        LocalDate this12MonthStart = nowPeriod.minusMonths(12);
        while (!this12MonthStart.isAfter(nowPeriod)) {
            Integer period = Integer.parseInt(this12MonthStart.format(DateTimeFormatter.ofPattern("yyyyMM")));
            if (!incomeMap.containsKey(period)) {
                CustomerServicePeriodMonthIncome income = new CustomerServicePeriodMonthIncome();
                income.setCustomerServiceId(customerService.getId());
                income.setAllTicketAmount(BigDecimal.ZERO);
                income.setPeriod(period);
                newIncomes.add(income);
            }
            this12MonthStart = this12MonthStart.plusMonths(1);
        }
        if (!ObjectUtils.isEmpty(newIncomes)) {
            customerServicePeriodMonthIncomeService.saveBatch(newIncomes);
        }
        baseMapper.updateDeliverStatusByCustomerServiceId(customerService.getId());
    }

    @Override
    @Async
    public void saveMonthPeriodListV2(CCustomerService customerService, List<CustomerServicePeriodMonth> monthPeriodList, List<CBusinessTagRelation> relations, List<CustomerServiceTaxTypeCheckVO> taxTypeCheckList) {
        if (ObjectUtils.isEmpty(monthPeriodList)) {
            return;
        }
        List<SysEmployee> advisorEmployees = Objects.isNull(monthPeriodList.get(0).getAdvisorDeptId()) ? Lists.newArrayList() :
                remoteEmployeeService.getEmployeeListByDeptId(monthPeriodList.get(0).getAdvisorDeptId()).getDataThrowException();
        List<SysEmployee> accountingEmployees = Objects.isNull(monthPeriodList.get(0).getAccountingDeptId()) ? Lists.newArrayList() :
                remoteEmployeeService.getEmployeeListByDeptId(monthPeriodList.get(0).getAccountingDeptId()).getDataThrowException();
        monthPeriodList.forEach(monthPeriod -> {
            save(monthPeriod);
            if (!ObjectUtils.isEmpty(advisorEmployees)) {
                customerServicePeriodEmployeeService.saveBatch(advisorEmployees.stream().map(advisorEmployee ->
                        new CCustomerServicePeriodEmployee().setPeriodId(monthPeriod.getId())
                                .setPeriodEmployeeType(1)
                                .setEmployeeId(advisorEmployee.getEmployeeId())
                                .setEmployeeName(advisorEmployee.getEmployeeName())
                ).collect(Collectors.toList()));
            }
            if (!ObjectUtils.isEmpty(accountingEmployees)) {
                customerServicePeriodEmployeeService.saveBatch(accountingEmployees.stream().map(accoutingEmployee ->
                        new CCustomerServicePeriodEmployee().setPeriodId(monthPeriod.getId())
                                .setPeriodEmployeeType(2)
                                .setEmployeeId(accoutingEmployee.getEmployeeId())
                                .setEmployeeName(accoutingEmployee.getEmployeeName())
                ).collect(Collectors.toList()));
            }
            if (!ObjectUtils.isEmpty(relations)) {
                relations.forEach(relation -> relation.setBusinessId(monthPeriod.getId()));
                businessTagRelationService.saveBatch(relations);
            }
            if (!ObjectUtils.isEmpty(taxTypeCheckList)) {
                customerServicePeriodMonthTaxTypeCheckService.saveByCheckVOList(monthPeriod.getId(), taxTypeCheckList);
            }
            customerServiceInAccountService.addInAccountFromPeriod(customerService, monthPeriod);
        });
        Map<Integer, CustomerServicePeriodMonthIncome> incomeMap = customerServicePeriodMonthIncomeService.selectByCustomerServiceId(customerService.getId()).stream().collect(Collectors.toMap(CustomerServicePeriodMonthIncome::getPeriod, Function.identity(), (v1, v2) -> v1));
        List<CustomerServicePeriodMonthIncome> newIncomes = monthPeriodList.stream().filter(monthPeriod -> !incomeMap.containsKey(monthPeriod.getPeriod())).map(monthPeriod ->
                new CustomerServicePeriodMonthIncome().setCustomerServiceId(monthPeriod.getCustomerServiceId())
                        .setPeriod(monthPeriod.getPeriod())
                        .setAllTicketAmount(BigDecimal.ZERO).setAllTicketTaxAmount(BigDecimal.ZERO).setNoTicketIncomeAmount(BigDecimal.ZERO)
        ).collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(newIncomes)) {
            customerServicePeriodMonthIncomeService.saveBatch(newIncomes);
        }
        // 全表更新一下收入
        Integer thisMonth = DateUtils.getNowPeriod();
        Map<String, LocalDate> thisYearMap = DateUtils.getStartEndByDateType(DateUtils.THIS_YEAR);
        Map<String, LocalDate> thisSeasonMap = DateUtils.getStartEndByDateType(DateUtils.THIS_SEASON);
        Map<String, LocalDate> this12MonthMap = DateUtils.getStartEndByDateType(DateUtils.THIS_12_MONTH);
        customerServiceMapper.updateCustomerServiceIncome(thisMonth,
                Integer.parseInt(DateUtils.localDateToStr(thisYearMap.get("startDate"), DateUtils.YYYYMM)),
                Integer.parseInt(DateUtils.localDateToStr(thisYearMap.get("endDate"), DateUtils.YYYYMM)),
                Integer.parseInt(DateUtils.localDateToStr(thisSeasonMap.get("startDate"), DateUtils.YYYYMM)),
                Integer.parseInt(DateUtils.localDateToStr(thisSeasonMap.get("endDate"), DateUtils.YYYYMM)),
                Integer.parseInt(DateUtils.localDateToStr(this12MonthMap.get("startDate"), DateUtils.YYYYMM)),
                Integer.parseInt(DateUtils.localDateToStr(this12MonthMap.get("endDate"), DateUtils.YYYYMM)), customerService.getId());
//        customerServiceMapper.updateCustomerServiceThisMonthIncome(thisMonth, null);
//        customerServiceMapper.updateCustomerServiceThisSeasonIncome(Integer.parseInt(DateUtils.localDateToStr(thisSeasonMap.get("startDate"), DateUtils.YYYYMM)),
//                Integer.parseInt(DateUtils.localDateToStr(thisSeasonMap.get("endDate"), DateUtils.YYYYMM)), null);
//        customerServiceMapper.updateCustomerServiceThisYearIncome(Integer.parseInt(DateUtils.localDateToStr(thisYearMap.get("startDate"), DateUtils.YYYYMM)),
//                Integer.parseInt(DateUtils.localDateToStr(thisYearMap.get("endDate"), DateUtils.YYYYMM)), null);
//        customerServiceMapper.updateCustomerServiceThis12MonthIncome(Integer.parseInt(DateUtils.localDateToStr(this12MonthMap.get("startDate"), DateUtils.YYYYMM)),
//                Integer.parseInt(DateUtils.localDateToStr(this12MonthMap.get("endDate"), DateUtils.YYYYMM)), null);
//        customerServiceMapper.updateCustomerServiceTicketTime(Integer.parseInt(DateUtils.localDateToStr(this12MonthMap.get("startDate"), DateUtils.YYYYMM)),
//                Integer.parseInt(DateUtils.localDateToStr(thisYearMap.get("endDate"), DateUtils.YYYYMM)), null);
    }

    @Override
    @Async
    public void updateMonthPeriodList(List<CustomerServicePeriodMonth> monthPeriodList, List<TagDTO> periodTags, Integer validPeriod, List<CustomerServiceTaxTypeCheckVO> taxTypeCheckList) {
        if (ObjectUtils.isEmpty(monthPeriodList)) {
            return;
        }
        Boolean hasCreateTax = !ObjectUtils.isEmpty(taxTypeCheckList) && taxTypeCheckList.get(0).getCustomerServiceId() > 0L;
        List<SysEmployee> advisorEmployees = Objects.isNull(monthPeriodList.get(0).getAdvisorDeptId()) ? Lists.newArrayList() :
                remoteEmployeeService.getEmployeeListByDeptId(monthPeriodList.get(0).getAdvisorDeptId()).getDataThrowException();
        List<SysEmployee> accountingEmployees = Objects.isNull(monthPeriodList.get(0).getAccountingDeptId()) ? Lists.newArrayList() :
                remoteEmployeeService.getEmployeeListByDeptId(monthPeriodList.get(0).getAccountingDeptId()).getDataThrowException();
        monthPeriodList.forEach(monthPeriod -> {
            if (monthPeriod.getPeriod() >= validPeriod) {
                update(new LambdaUpdateWrapper<CustomerServicePeriodMonth>()
                        .set(CustomerServicePeriodMonth::getCustomerName, monthPeriod.getCustomerName())
                        .set(CustomerServicePeriodMonth::getBusinessDeptId, monthPeriod.getBusinessDeptId())
                        .set(CustomerServicePeriodMonth::getBusinessTopDeptId, monthPeriod.getBusinessTopDeptId())
                        .set(CustomerServicePeriodMonth::getCreditCode, monthPeriod.getCreditCode())
                        .set(CustomerServicePeriodMonth::getTaxNumber, monthPeriod.getTaxNumber())
                        .set(CustomerServicePeriodMonth::getServiceNumber, monthPeriod.getServiceNumber())
                        .set(CustomerServicePeriodMonth::getTaxType, monthPeriod.getTaxType())
                        .set(CustomerServicePeriodMonth::getAdvisorDeptId, monthPeriod.getAdvisorDeptId())
                        .set(CustomerServicePeriodMonth::getAdvisorTopDeptId, monthPeriod.getAdvisorTopDeptId())
                        .set(CustomerServicePeriodMonth::getAccountingTopDeptId, monthPeriod.getAccountingTopDeptId())
                        .set(CustomerServicePeriodMonth::getAccountingDeptId, monthPeriod.getAccountingDeptId())
                        .eq(CustomerServicePeriodMonth::getId, monthPeriod.getId()));
                customerServicePeriodEmployeeService.deleteByPeriodId(monthPeriod.getId());
                if (!ObjectUtils.isEmpty(advisorEmployees)) {
                    customerServicePeriodEmployeeService.saveBatch(advisorEmployees.stream().map(advisorEmployee ->
                            new CCustomerServicePeriodEmployee().setPeriodId(monthPeriod.getId())
                                    .setPeriodEmployeeType(1)
                                    .setEmployeeId(advisorEmployee.getEmployeeId())
                                    .setEmployeeName(advisorEmployee.getEmployeeName())
                    ).collect(Collectors.toList()));
                }
                if (!ObjectUtils.isEmpty(accountingEmployees)) {
                    customerServicePeriodEmployeeService.saveBatch(accountingEmployees.stream().map(accoutingEmployee ->
                            new CCustomerServicePeriodEmployee().setPeriodId(monthPeriod.getId())
                                    .setPeriodEmployeeType(2)
                                    .setEmployeeId(accoutingEmployee.getEmployeeId())
                                    .setEmployeeName(accoutingEmployee.getEmployeeName())
                    ).collect(Collectors.toList()));
                }
                businessTagRelationService.deleteByBusinessIdAndBusinessType(monthPeriod.getId(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD);
                if (!ObjectUtils.isEmpty(periodTags)) {
                    businessTagRelationService.saveByBusinessIdAndBusinessType(monthPeriod.getId(), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD, periodTags);
                }
                if (!hasCreateTax) {
                    customerServicePeriodMonthTaxTypeCheckService.deleteByCustomerServicePeriodMonthId(monthPeriod.getId());
                    customerServicePeriodMonthTaxTypeCheckService.saveByCheckVOList(monthPeriod.getId(), taxTypeCheckList);
                }
            } else {
                updateById(new CustomerServicePeriodMonth().setId(monthPeriod.getId()).setCustomerName(monthPeriod.getCustomerName())
                        .setCreditCode(monthPeriod.getCreditCode())
                        .setTaxNumber(monthPeriod.getTaxNumber())
                        .setServiceNumber(monthPeriod.getServiceNumber()));
//                customerServiceInAccountService.update(new LambdaUpdateWrapper<CustomerServiceInAccount>()
//                        .eq(CustomerServiceInAccount::getCustomerServicePeriodMonthId, monthPeriod.getId())
//                        .eq(CustomerServiceInAccount::getIsDel, false)
//                        .set(CustomerServiceInAccount::getCustomerName, monthPeriod.getCustomerName()));
            }
        });
    }

    @Override
    @Async
    public void deleteMonthPeriodList(List<Long> periodIds) {
        if (ObjectUtils.isEmpty(periodIds)) {
            return;
        }
        periodIds.forEach(periodId -> {
            removeById(periodId);
            customerServicePeriodEmployeeService.deleteByPeriodId(periodId);
            businessTagRelationService.deleteByBusinessIdAndBusinessType(periodId, TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD);
            customerServicePeriodMonthTaxTypeCheckService.deleteByCustomerServicePeriodMonthId(periodId);
        });
    }

    @Override
    @Transactional
    public void updateBusinessDept(CustomerServiceChangeBusinessDeptVO vo, List<Long> customerServiceIds, Long topBusinessDeptId, Integer validPeriod) {
        List<CustomerServicePeriodMonth> periodList = list(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .in(CustomerServicePeriodMonth::getCustomerServiceId, customerServiceIds)
                .ge(CustomerServicePeriodMonth::getPeriod, validPeriod));
        if (ObjectUtils.isEmpty(periodList)) {
            return;
        }
        List<Long> periodIds = periodList.stream().map(CustomerServicePeriodMonth::getId).collect(Collectors.toList());
        update(new LambdaUpdateWrapper<CustomerServicePeriodMonth>()
                .in(CustomerServicePeriodMonth::getId, periodIds)
                .set(CustomerServicePeriodMonth::getBusinessDeptId, vo.getDeptId())
                .set(CustomerServicePeriodMonth::getBusinessTopDeptId, topBusinessDeptId)
                .set(CustomerServicePeriodMonth::getAdvisorDeptId, null)
                .set(CustomerServicePeriodMonth::getAdvisorTopDeptId, vo.getDeptId()));
        customerServicePeriodEmployeeService.remove(new LambdaQueryWrapper<CCustomerServicePeriodEmployee>()
                .in(CCustomerServicePeriodEmployee::getPeriodId, periodIds)
                .eq(CCustomerServicePeriodEmployee::getPeriodEmployeeType, 1));
    }

    @Override
    @Transactional
    public void updateAdvisorDept(CustomerServiceDispatchVO vo, List<Long> customerServiceIds, Long topAdvisorDeptId, Long businessTopDeptId, Integer validPeriod) {
        List<CustomerServicePeriodMonth> periodList = list(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .in(CustomerServicePeriodMonth::getCustomerServiceId, customerServiceIds)
                .ge(!Objects.isNull(validPeriod), CustomerServicePeriodMonth::getPeriod, validPeriod)
                .eq(Objects.isNull(validPeriod), CustomerServicePeriodMonth::getBusinessDeptId, topAdvisorDeptId));
        if (ObjectUtils.isEmpty(periodList)) {
            return;
        }
        List<Long> periodIds = periodList.stream().map(CustomerServicePeriodMonth::getId).collect(Collectors.toList());
        update(new LambdaUpdateWrapper<CustomerServicePeriodMonth>()
                .in(CustomerServicePeriodMonth::getId, periodIds)
                .set(CustomerServicePeriodMonth::getAdvisorDeptId, vo.getDeptId())
                .set(CustomerServicePeriodMonth::getAdvisorTopDeptId, topAdvisorDeptId)
                .set(CustomerServicePeriodMonth::getBusinessDeptId, topAdvisorDeptId)
                .set(CustomerServicePeriodMonth::getBusinessTopDeptId, businessTopDeptId));
        customerServicePeriodEmployeeService.remove(new LambdaQueryWrapper<CCustomerServicePeriodEmployee>()
                .in(CCustomerServicePeriodEmployee::getPeriodId, periodIds)
                .eq(CCustomerServicePeriodEmployee::getPeriodEmployeeType, 1));
        List<SysEmployee> advisorEmployees = Objects.isNull(vo.getDeptId()) ? Lists.newArrayList() : remoteEmployeeService.getEmployeeListByDeptId(vo.getDeptId()).getDataThrowException();
        if (!ObjectUtils.isEmpty(advisorEmployees)) {
            periodList.forEach(period ->
                    customerServicePeriodEmployeeService.saveBatch(advisorEmployees.stream().map(advisorEmployee ->
                            new CCustomerServicePeriodEmployee().setPeriodId(period.getId())
                                    .setPeriodEmployeeType(1)
                                    .setEmployeeId(advisorEmployee.getEmployeeId())
                                    .setEmployeeName(advisorEmployee.getEmployeeName())
                    ).collect(Collectors.toList())));
        }
    }

    @Override
    @Transactional
    public void updateAccountingDept(CustomerServiceDispatchVO vo, List<Long> customerServiceIds, Long topAccountingDeptId, Integer validPeriod) {
        List<CustomerServicePeriodMonth> periodList = list(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .in(CustomerServicePeriodMonth::getCustomerServiceId, customerServiceIds)
                .ge(!Objects.isNull(validPeriod), CustomerServicePeriodMonth::getPeriod, validPeriod));
        if (ObjectUtils.isEmpty(periodList)) {
            return;
        }
        List<Long> periodIds = periodList.stream().map(CustomerServicePeriodMonth::getId).collect(Collectors.toList());
        update(new LambdaUpdateWrapper<CustomerServicePeriodMonth>()
                .in(CustomerServicePeriodMonth::getId, periodIds)
                .set(CustomerServicePeriodMonth::getAccountingDeptId, vo.getDeptId())
                .set(CustomerServicePeriodMonth::getAccountingTopDeptId, topAccountingDeptId));
        customerServicePeriodEmployeeService.remove(new LambdaQueryWrapper<CCustomerServicePeriodEmployee>()
                .in(CCustomerServicePeriodEmployee::getPeriodId, periodIds)
                .eq(CCustomerServicePeriodEmployee::getPeriodEmployeeType, 2));
        List<SysEmployee> accoutingEmployees = Objects.isNull(vo.getDeptId()) ? Lists.newArrayList() : remoteEmployeeService.getEmployeeListByDeptId(vo.getDeptId()).getDataThrowException();
        if (!ObjectUtils.isEmpty(accoutingEmployees)) {
            periodList.forEach(period ->
                    customerServicePeriodEmployeeService.saveBatch(accoutingEmployees.stream().map(advisorEmployee ->
                            new CCustomerServicePeriodEmployee().setPeriodId(period.getId())
                                    .setPeriodEmployeeType(2)
                                    .setEmployeeId(advisorEmployee.getEmployeeId())
                                    .setEmployeeName(advisorEmployee.getEmployeeName())
                    ).collect(Collectors.toList())));
        }
    }

    @Override
    @Transactional
    public void updateAccountingTopDept(List<Long> customerServiceIds, Long accountingTopDeptId) {
        List<CustomerServicePeriodMonth> periodList = list(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .in(CustomerServicePeriodMonth::getCustomerServiceId, customerServiceIds)
                .eq(CustomerServicePeriodMonth::getPeriod, DateUtils.getNowPeriod()));
        if (ObjectUtils.isEmpty(periodList)) {
            return;
        }
        List<Long> periodIds = periodList.stream().map(CustomerServicePeriodMonth::getId).collect(Collectors.toList());
        update(new LambdaUpdateWrapper<CustomerServicePeriodMonth>()
                .in(CustomerServicePeriodMonth::getId, periodIds)
                .set(CustomerServicePeriodMonth::getAccountingDeptId, null)
                .set(CustomerServicePeriodMonth::getAccountingTopDeptId, accountingTopDeptId));
        customerServicePeriodEmployeeService.remove(new LambdaQueryWrapper<CCustomerServicePeriodEmployee>()
                .in(CCustomerServicePeriodEmployee::getPeriodId, periodIds)
                .eq(CCustomerServicePeriodEmployee::getPeriodEmployeeType, 2));
    }

    @Override
    public CustomerServicePeriodMonth selectLastPeriodByCustomerServiceId(Long customerServiceId) {
        if (Objects.isNull(customerServiceId)) {
            return null;
        }
        return getOne(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .eq(CustomerServicePeriodMonth::getCustomerServiceId, customerServiceId)
                .orderByDesc(CustomerServicePeriodMonth::getPeriod).last("limit 1"));
    }

    @Override
    public List<CustomerServicePeriodMonth> selectByCustomerServiceId(Long customerServiceId) {
        if (Objects.isNull(customerServiceId)) {
            return Lists.newArrayList();
        }
        return list(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .eq(CustomerServicePeriodMonth::getCustomerServiceId, customerServiceId)
                .orderByAsc(CustomerServicePeriodMonth::getPeriod));
    }

    @Override
    public List<CustomerServicePeriodMonth> periodSelect(Long customerServiceId, Integer tabType) {
        LambdaQueryWrapper<CustomerServicePeriodMonth> queryWrapper = new LambdaQueryWrapper<CustomerServicePeriodMonth>().eq(CustomerServicePeriodMonth::getCustomerServiceId, customerServiceId)
                .orderByDesc(CustomerServicePeriodMonth::getPeriod);
        if (!Objects.isNull(tabType)) {
            List<Integer> samePeriods = Lists.newArrayList();
            if (tabType == 2 || tabType == 1) {
                List<Integer> medicalCustomerDeliverPeriods = customerDeliverMapper.selectList(new LambdaQueryWrapper<CustomerDeliver>()
                        .eq(CustomerDeliver::getCustomerServiceId, customerServiceId)
                        .eq(CustomerDeliver::getIsDel, false)
                        .eq(CustomerDeliver::getDeliverType, DeliverType.MEDICAL_INSURANCE.getCode())
                        .select(CustomerDeliver::getPeriod)).stream().map(CustomerDeliver::getPeriod).collect(Collectors.toList());
                List<Integer> socialCustomerDeliverPeriods = customerDeliverMapper.selectList(new LambdaQueryWrapper<CustomerDeliver>()
                        .eq(CustomerDeliver::getCustomerServiceId, customerServiceId)
                        .eq(CustomerDeliver::getIsDel, false)
                        .eq(CustomerDeliver::getDeliverType, DeliverType.SOCIAL_INSURANCE.getCode())
                        .select(CustomerDeliver::getPeriod)).stream().map(CustomerDeliver::getPeriod).collect(Collectors.toList());
                samePeriods = medicalCustomerDeliverPeriods.stream().filter(socialCustomerDeliverPeriods::contains).collect(Collectors.toList());
            } else {
                samePeriods = customerDeliverMapper.selectList(new LambdaQueryWrapper<CustomerDeliver>()
                        .eq(CustomerDeliver::getCustomerServiceId, customerServiceId)
                        .eq(CustomerDeliver::getIsDel, false)
                        .eq(CustomerDeliver::getDeliverType, tabType)
                        .select(CustomerDeliver::getPeriod)).stream().map(CustomerDeliver::getPeriod).collect(Collectors.toList());
            }
            if (!ObjectUtils.isEmpty(samePeriods)) {
                queryWrapper.notIn(CustomerServicePeriodMonth::getPeriod, samePeriods);
            }
        }
        return list(queryWrapper);
    }

    @Override
    public List<CustomerServicePeriodMonth> periodSelectForDeliver(Long customerServiceId, Integer tabType, Long deptId) {
        LambdaQueryWrapper<CustomerServicePeriodMonth> queryWrapper = new LambdaQueryWrapper<CustomerServicePeriodMonth>().eq(CustomerServicePeriodMonth::getCustomerServiceId, customerServiceId)
                .orderByDesc(CustomerServicePeriodMonth::getPeriod);
        if (!Objects.isNull(tabType)) {
            List<Integer> samePeriods = Lists.newArrayList();
            if (tabType <= 2) {
                List<Integer> medicalCustomerDeliverPeriods = customerDeliverMapper.selectList(new LambdaQueryWrapper<CustomerDeliver>()
                        .eq(CustomerDeliver::getCustomerServiceId, customerServiceId)
                        .eq(CustomerDeliver::getIsDel, false)
                        .eq(CustomerDeliver::getDeliverType, DeliverType.MEDICAL_INSURANCE.getCode())
                        .select(CustomerDeliver::getPeriod)).stream().map(CustomerDeliver::getPeriod).collect(Collectors.toList());
                List<Integer> socialCustomerDeliverPeriods = customerDeliverMapper.selectList(new LambdaQueryWrapper<CustomerDeliver>()
                        .eq(CustomerDeliver::getCustomerServiceId, customerServiceId)
                        .eq(CustomerDeliver::getIsDel, false)
                        .eq(CustomerDeliver::getDeliverType, DeliverType.SOCIAL_INSURANCE.getCode())
                        .select(CustomerDeliver::getPeriod)).stream().map(CustomerDeliver::getPeriod).collect(Collectors.toList());
                samePeriods = medicalCustomerDeliverPeriods.stream().filter(socialCustomerDeliverPeriods::contains).collect(Collectors.toList());
                queryWrapper.eq(CustomerServicePeriodMonth::getServiceType, CustomerServicePeriodMonthServiceType.DO_ACCOUNT.getCode());
            } else if (tabType <= 6) {
                samePeriods = customerDeliverMapper.selectList(new LambdaQueryWrapper<CustomerDeliver>()
                        .eq(CustomerDeliver::getCustomerServiceId, customerServiceId)
                        .eq(CustomerDeliver::getIsDel, false)
                        .eq(CustomerDeliver::getDeliverType, tabType)
                        .select(CustomerDeliver::getPeriod)).stream().map(CustomerDeliver::getPeriod).collect(Collectors.toList());
                queryWrapper.eq(CustomerServicePeriodMonth::getServiceType, CustomerServicePeriodMonthServiceType.DO_ACCOUNT.getCode());
            } else {
                samePeriods = customerDeliverMapper.selectList(new LambdaQueryWrapper<CustomerDeliver>()
                        .eq(CustomerDeliver::getCustomerServiceId, customerServiceId)
                        .eq(CustomerDeliver::getIsDel, false)
                        .eq(CustomerDeliver::getDeliverType, tabType)
                        .select(CustomerDeliver::getPeriod)).stream().map(CustomerDeliver::getPeriod).collect(Collectors.toList());
                Integer lastYear12Period = (LocalDate.now().getYear() - 1) * 100 + 12;
                queryWrapper.eq(CustomerServicePeriodMonth::getPeriod, lastYear12Period);
            }
            if (!ObjectUtils.isEmpty(samePeriods)) {
                queryWrapper.notIn(CustomerServicePeriodMonth::getPeriod, samePeriods);
            }
        }
        List<CustomerServicePeriodMonth> list = list(queryWrapper);
        if (!ObjectUtils.isEmpty(list)) {
            Map<Long, List<TagDTO>> tagMap = businessTagRelationService.getTagsByBusinessTypeForList(list.stream().map(CustomerServicePeriodMonth::getId).collect(Collectors.toList()), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD);
            list.forEach(e -> {
                e.setHasMedical(tagMap.getOrDefault(e.getId(), Lists.newArrayList()).stream().anyMatch(t -> Objects.equals(t.getId(), specialTagProperties.getYibao())));
                e.setHasSocial(tagMap.getOrDefault(e.getId(), Lists.newArrayList()).stream().anyMatch(t -> Objects.equals(t.getId(), specialTagProperties.getShebao())));
            });
        }
        return list;
    }

    @Override
    public List<CustomerServicePeriodMonth> periodSelectForDeliverV2(Long customerServiceId, Integer tabType, Long deptId) {
        UserDeptDTO userDept = remoteDeptService.userDeptList(SecurityUtils.getUserId(), deptId).getDataThrowException();
        if (!userDept.getIsAdmin() && ObjectUtils.isEmpty(userDept.getDeptIds())) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<CustomerServicePeriodMonth> queryWrapper = new LambdaQueryWrapper<CustomerServicePeriodMonth>().eq(CustomerServicePeriodMonth::getCustomerServiceId, customerServiceId)
                .orderByDesc(CustomerServicePeriodMonth::getPeriod);
        if (!Objects.isNull(tabType)) {
            List<Integer> samePeriods = Lists.newArrayList();
            if (tabType <= 2) {
                List<Integer> medicalCustomerDeliverPeriods = customerDeliverMapper.selectList(new LambdaQueryWrapper<CustomerDeliver>()
                        .eq(CustomerDeliver::getCustomerServiceId, customerServiceId)
                        .eq(CustomerDeliver::getIsDel, false)
                        .eq(CustomerDeliver::getDeliverType, DeliverType.MEDICAL_INSURANCE.getCode())
                        .select(CustomerDeliver::getPeriod)).stream().map(CustomerDeliver::getPeriod).collect(Collectors.toList());
                List<Integer> socialCustomerDeliverPeriods = customerDeliverMapper.selectList(new LambdaQueryWrapper<CustomerDeliver>()
                        .eq(CustomerDeliver::getCustomerServiceId, customerServiceId)
                        .eq(CustomerDeliver::getIsDel, false)
                        .eq(CustomerDeliver::getDeliverType, DeliverType.SOCIAL_INSURANCE.getCode())
                        .select(CustomerDeliver::getPeriod)).stream().map(CustomerDeliver::getPeriod).collect(Collectors.toList());
                samePeriods = medicalCustomerDeliverPeriods.stream().filter(socialCustomerDeliverPeriods::contains).collect(Collectors.toList());
                queryWrapper.eq(CustomerServicePeriodMonth::getServiceType, CustomerServicePeriodMonthServiceType.DO_ACCOUNT.getCode());
            } else if (tabType <= 6) {
                samePeriods = customerDeliverMapper.selectList(new LambdaQueryWrapper<CustomerDeliver>()
                        .eq(CustomerDeliver::getCustomerServiceId, customerServiceId)
                        .eq(CustomerDeliver::getIsDel, false)
                        .eq(CustomerDeliver::getDeliverType, tabType)
                        .select(CustomerDeliver::getPeriod)).stream().map(CustomerDeliver::getPeriod).collect(Collectors.toList());
                queryWrapper.eq(CustomerServicePeriodMonth::getServiceType, CustomerServicePeriodMonthServiceType.DO_ACCOUNT.getCode());
            } else if (tabType <= 9) {
                samePeriods = customerDeliverMapper.selectList(new LambdaQueryWrapper<CustomerDeliver>()
                        .eq(CustomerDeliver::getCustomerServiceId, customerServiceId)
                        .eq(CustomerDeliver::getIsDel, false)
                        .eq(CustomerDeliver::getDeliverType, tabType)
                        .select(CustomerDeliver::getPeriod)).stream().map(CustomerDeliver::getPeriod).collect(Collectors.toList());
                Integer lastYear12Period = (LocalDate.now().getYear() - 1) * 100 + 12;
                queryWrapper.eq(CustomerServicePeriodMonth::getPeriod, lastYear12Period);
            }
            if (!ObjectUtils.isEmpty(samePeriods)) {
                queryWrapper.notIn(CustomerServicePeriodMonth::getPeriod, samePeriods);
            }
        }
        if (!ObjectUtils.isEmpty(userDept.getDeptIds())) {
            if (userDept.getDeptType() == 1) {
                queryWrapper.and(wrapper -> wrapper.in(CustomerServicePeriodMonth::getAdvisorDeptId, userDept.getDeptIds()).or().in(CustomerServicePeriodMonth::getAdvisorTopDeptId, userDept.getDeptIds()));
            } else {
                queryWrapper.and(wrapper -> wrapper.in(CustomerServicePeriodMonth::getAccountingDeptId, userDept.getDeptIds()).or().in(CustomerServicePeriodMonth::getAccountingTopDeptId, userDept.getDeptIds()));
            }
        }
        List<CustomerServicePeriodMonth> list = list(queryWrapper);
        if (!ObjectUtils.isEmpty(list)) {
            Map<Long, List<TagDTO>> tagMap = tabType != 2 ? Maps.newHashMap() :
                    businessTagRelationService.getTagsByBusinessTypeForList(list.stream().map(CustomerServicePeriodMonth::getId).collect(Collectors.toList()), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD);
            Map<String, CustomerServicePeriodMonthIncome> incomeMap = tabType != 4 ? Maps.newHashMap() :
                    customerServicePeriodMonthIncomeService.getCustomerServiceIncomeByCustomerServiceIdAndPeriod(list.stream().map(row -> RemoteCustomerServiceIncomeSearchVO.builder()
                            .customerServiceId(row.getCustomerServiceId())
                            .period(row.getPeriod()).build()).collect(Collectors.toList())).stream().collect(Collectors.toMap(row -> row.getCustomerServiceId() + "_" + row.getPeriod(), Function.identity(), (v1, v2) -> v1));
            list.forEach(e -> {
                if (tabType == 2) {
                    e.setHasMedical(tagMap.getOrDefault(e.getId(), Lists.newArrayList()).stream().anyMatch(t -> Objects.equals(t.getId(), specialTagProperties.getYibao())));
                    e.setHasSocial(tagMap.getOrDefault(e.getId(), Lists.newArrayList()).stream().anyMatch(t -> Objects.equals(t.getId(), specialTagProperties.getShebao())));
                }
                if (tabType == 4) {
                    CustomerServicePeriodMonthIncome income = incomeMap.get(e.getCustomerServiceId() + "_" + e.getPeriod());
                    e.setNoTicketIncome(Objects.isNull(income) || Objects.isNull(income.getNoTicketIncomeAmount()) ? null : income.getNoTicketIncomeAmount());
                }
            });
        }
        return list;
    }

    @Override
    public List<CustomerServicePeriodMonth> getCustomerPeriodByCreditCodeAndPeriod(CustomerPeriodVO vo) {
        if (ObjectUtils.isEmpty(vo.getCreditCodes()) || Objects.isNull(vo.getPeriod())) {
            return Collections.emptyList();
        }
        List<CustomerServicePeriodMonth> list = list(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .eq(CustomerServicePeriodMonth::getPeriod, vo.getPeriod())
                .in(CustomerServicePeriodMonth::getCreditCode, vo.getCreditCodes()));
        if (!ObjectUtils.isEmpty(list)) {
            Map<Long, List<TagDTO>> tagMap = businessTagRelationService.getTagsByBusinessTypeForList(list.stream().map(CustomerServicePeriodMonth::getId).collect(Collectors.toList()), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD);
            list.forEach(e -> {
                e.setHasMedical(tagMap.getOrDefault(e.getId(), Lists.newArrayList()).stream().anyMatch(t -> Objects.equals(t.getId(), specialTagProperties.getYibao())));
                e.setHasSocial(tagMap.getOrDefault(e.getId(), Lists.newArrayList()).stream().anyMatch(t -> Objects.equals(t.getId(), specialTagProperties.getShebao())));
            });
        }
        return list;
    }

    @Override
    public List<CustomerServicePeriodMonth> selectByTaxTypeAndPeriodAndStatus(Integer taxType, Integer period, ServiceStatus serviceStatus) {
        return list(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .eq(!Objects.isNull(taxType), CustomerServicePeriodMonth::getTaxType, taxType)
                .eq(CustomerServicePeriodMonth::getPeriod, period)
                .eq(CustomerServicePeriodMonth::getServiceStatus, serviceStatus.getCode()));
    }

    @Override
    public List<CustomerServicePeriodMonth> incomePeriodSelect(Long customerServiceId) {
        LambdaQueryWrapper<CustomerServicePeriodMonth> queryWrapper = new LambdaQueryWrapper<CustomerServicePeriodMonth>().eq(CustomerServicePeriodMonth::getCustomerServiceId, customerServiceId)
                .orderByDesc(CustomerServicePeriodMonth::getPeriod);
        queryWrapper.notInSql(CustomerServicePeriodMonth::getPeriod, "select distinct period from c_customer_service_period_month_income where customer_service_id = " + customerServiceId);
        return list(queryWrapper);
    }

    @Override
    public void updatePeriodTaxCheck(Long customerServiceId, List<CustomerServiceTaxTypeCheckVO> taxTypes) {
        CustomerServicePeriodMonth nowPeriod = selectByCustomerServiceIdAndPeriod(customerServiceId, DateUtils.getNowPeriod());
        if (!Objects.isNull(nowPeriod)) {
            customerServicePeriodMonthTaxTypeCheckService.deleteByCustomerServicePeriodMonthId(nowPeriod.getId());
            List<CustomerServiceTaxTypeCheckVO> periodTaxTypes = getPeriodTaxTypes(nowPeriod.getPeriod(), taxTypes);
            if (!ObjectUtils.isEmpty(periodTaxTypes)) {
                customerServicePeriodMonthTaxTypeCheckService.saveByCheckVOList(nowPeriod.getId(), periodTaxTypes);
            }
        }
        // 还要修改上个月
        CustomerServicePeriodMonth prePeriod = selectByCustomerServiceIdAndPeriod(customerServiceId, DateUtils.getPrePeriod());
        if (!Objects.isNull(prePeriod)) {
            customerServicePeriodMonthTaxTypeCheckService.deleteByCustomerServicePeriodMonthId(prePeriod.getId());
            List<CustomerServiceTaxTypeCheckVO> periodTaxTypes = getPeriodTaxTypes(prePeriod.getPeriod(), taxTypes);
            if (!ObjectUtils.isEmpty(periodTaxTypes)) {
                customerServicePeriodMonthTaxTypeCheckService.saveByCheckVOList(prePeriod.getId(), periodTaxTypes);
            }
        }
    }

    private List<CustomerServiceTaxTypeCheckVO> getPeriodTaxTypes(Integer period, List<CustomerServiceTaxTypeCheckVO> taxTypes) {
        if (Objects.isNull(period)) {
            return Lists.newArrayList();
        }
        if (ObjectUtils.isEmpty(taxTypes)) {
            return Lists.newArrayList();
        }
        taxTypes = taxTypes.stream().filter(t -> !t.getTaxTypeName().contains("社保")).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(taxTypes)) {
            return Lists.newArrayList();
        }
        Integer month = period % 100;
        if (Lists.newArrayList(1, 2, 4, 5, 7, 8, 10, 11).contains(month)) {
            return taxTypes.stream().filter(t -> t.getReportType() == 1 || t.getReportType() == 4).collect(Collectors.toList());
        } else if (Lists.newArrayList(3, 9).contains(month)) {
            return taxTypes.stream().filter(t -> t.getReportType() == 1 || t.getReportType() == 2 || t.getReportType() == 4).collect(Collectors.toList());
        } else if (month == 6) {
            return taxTypes.stream().filter(t -> t.getReportType() == 1 || t.getReportType() == 2 || t.getReportType() == 4 || t.getReportType() == 5).collect(Collectors.toList());
        } else {
            return taxTypes.stream().filter(t -> t.getReportType() == 1 || t.getReportType() == 2 || t.getReportType() == 3 || t.getReportType() == 4 || t.getReportType() == 5).collect(Collectors.toList());
        }
    }

    @Override
    public void deletePeriodMonthTagRelationByCustomerIds(List<Long> customerServiceIds, Integer startPeriod, Integer endPeriod) {
        if (ObjectUtils.isEmpty(customerServiceIds)) {
            return;
        }
        List<CustomerServicePeriodMonth> periods = list(new LambdaQueryWrapper<CustomerServicePeriodMonth>().in(CustomerServicePeriodMonth::getCustomerServiceId, customerServiceIds)
                .le(!Objects.isNull(endPeriod), CustomerServicePeriodMonth::getPeriod, endPeriod)
                .ge(!Objects.isNull(startPeriod), CustomerServicePeriodMonth::getPeriod, startPeriod)
                .select(CustomerServicePeriodMonth::getId));
        if (ObjectUtils.isEmpty(periods)) {
            return;
        }
        baseMapper.deletePeriodMonthTagRelationByPeriodIds(periods.stream().map(CustomerServicePeriodMonth::getId).collect(Collectors.toList()));
    }

    @Override
    public void deletePeriodMonthTaxCheckByCustomerIds(List<Long> customerServiceIds, Integer startPeriod, Integer endPeriod) {
        if (ObjectUtils.isEmpty(customerServiceIds)) {
            return;
        }
        List<CustomerServicePeriodMonth> periods = list(new LambdaQueryWrapper<CustomerServicePeriodMonth>().in(CustomerServicePeriodMonth::getCustomerServiceId, customerServiceIds)
                .le(!Objects.isNull(endPeriod), CustomerServicePeriodMonth::getPeriod, endPeriod)
                .ge(!Objects.isNull(startPeriod), CustomerServicePeriodMonth::getPeriod, startPeriod)
                .select(CustomerServicePeriodMonth::getId));
        if (ObjectUtils.isEmpty(periods)) {
            return;
        }
        baseMapper.deletePeriodMonthTaxCheckByPeriodIds(periods.stream().map(CustomerServicePeriodMonth::getId).collect(Collectors.toList()));
    }

    @Override
    public void savePeriodMonthTagRelationByCustomerIds(List<Long> customerServiceIds, Integer startPeriod, Integer endPeriod) {
        if (ObjectUtils.isEmpty(customerServiceIds)) {
            return;
        }
        baseMapper.savePeriodMonthTagRelationByCustomerIds(customerServiceIds, startPeriod, endPeriod);
    }

    @Override
    public void savePeriodMonthEmployeeByCustomerIds(List<Long> customerServiceIds) {
        if (ObjectUtils.isEmpty(customerServiceIds)) {
            return;
        }
        baseMapper.savePeriodMonthAdvisorEmployeeByCustomerIds(customerServiceIds);
        baseMapper.savePeriodMonthAccountingEmployeeByCustomerIds(customerServiceIds);
    }

    @Override
    public void savePeriodMonthTaxCheckByCustomerIds(List<Long> customerServiceIds, Integer startPeriod, Integer endPeriod) {
        if (ObjectUtils.isEmpty(customerServiceIds)) {
            return;
        }
        baseMapper.savePeriodMonthTaxCheckByExistsTaxCheckCustomerIds(customerServiceIds, startPeriod, endPeriod);
        baseMapper.savePeriodMonthTaxCheckByNotExistsTaxCheckSmallCustomerIds(customerServiceIds, startPeriod, endPeriod);
        baseMapper.savePeriodMonthTaxCheckByNotExistsTaxCheckCommlyCustomerIds(customerServiceIds, startPeriod, endPeriod);
    }

    @Override
    public List<RemoteCustomerPeriodDTO> getCustomerPeriodByPeriodRange(Integer periodMin, Integer periodMax, Long deptId, Long userId) {
        UserDeptDTO userDeptDTO = remoteDeptService.userDeptList(userId, deptId).getDataThrowException();
        if (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<CustomerServicePeriodMonth> queryWrapper = new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .ge(CustomerServicePeriodMonth::getPeriod, periodMin)
                .le(CustomerServicePeriodMonth::getPeriod, periodMax);
        if (!ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
            if (userDeptDTO.getDeptType() == 1) {
                queryWrapper.and(wrapper -> wrapper.in(CustomerServicePeriodMonth::getAdvisorDeptId, userDeptDTO.getDeptIds())
                        .or().in(CustomerServicePeriodMonth::getAdvisorTopDeptId, userDeptDTO.getDeptIds()));
            } else {
                queryWrapper.and(wrapper -> wrapper.in(CustomerServicePeriodMonth::getAccountingDeptId, userDeptDTO.getDeptIds())
                        .or().in(CustomerServicePeriodMonth::getAccountingTopDeptId, userDeptDTO.getDeptIds()));
            }
        }
        List<CustomerServicePeriodMonth> periodList = list(queryWrapper);
        return periodList.stream().map(period -> RemoteCustomerPeriodDTO.builder()
                .id(period.getId())
                .customerServiceId(period.getCustomerServiceId())
                .customerName(period.getCustomerName())
                .creditCode(period.getCreditCode()).build()).collect(Collectors.toList());
    }

    @Override
    public List<CustomerServicePeriodMonth> getNoInAccount() {
        return customerServicePeriodMonthMapper.selectNoInAccount();
    }

    @Override
    public List<CustomerServicePeriodMonth> getNoInAccountByCustomerService(Long customerServiceId) {
        return customerServicePeriodMonthMapper.selectNoInAccountByCustomerService(customerServiceId);
    }

    @Override
    public List<CustomerServicePeriodMonth> getNoInAccountByCustomerServiceBatch(List<Long> customerServiceIds) {
        return customerServicePeriodMonthMapper.selectNoInAccountByCustomerServiceBatch(customerServiceIds);
    }

    @Override
    public void saveMonthPeriodListFromOther(CCustomerService cCustomerService, List<TagDTO> tagDTOS, Integer startPeriod, Integer endPeriod, Integer addFromType, Long addFromId,
                                             Long accountingDeptId, Long deptId, Long userId, String operName) {
        Long customerServiceId = cCustomerService.getId();

        List<CustomerServiceTaxTypeCheckVO> taxTypeCheckList = customerTaxTypeCheckService.selectCustomerTaxTypeCheckByCustomerServiceId(customerServiceId, cCustomerService.getTaxType());
        SysDept sysDept = remoteDeptService.getDeptInfo(accountingDeptId).getDataThrowException();

        List<Integer> yearPeriodList = Lists.newArrayList();
        List<CustomerServicePeriodMonth> monthPeriodList = Lists.newArrayList();
        LocalDate startMonth = DateUtils.strToLocalDate(startPeriod.toString(), DateUtils.YYYYMMDD);
        LocalDate endMonth = DateUtils.strToLocalDate(endPeriod.toString(), DateUtils.YYYYMMDD);
        List<CustomerServiceBankAccount> bankList = customerServiceBankAccountService.selectByCustomerServiceId(cCustomerService.getId());
        while (!startMonth.isAfter(endMonth)) {
            int period = Integer.parseInt(DateUtils.localDateToStr(startMonth, DateUtils.YYYYMM));
            boolean hasPeriodBank = !ObjectUtils.isEmpty(bankList) && bankList.stream().anyMatch(bank -> (Objects.isNull(bank.getAccountOpenDate()) || Integer.parseInt(bank.getAccountOpenDate().format(DateTimeFormatter.ofPattern("yyyyMM"))) <= period)
                    && (Objects.isNull(bank.getAccountCloseDate()) || Integer.parseInt(bank.getAccountCloseDate().format(DateTimeFormatter.ofPattern("yyyyMM"))) >= period));
            monthPeriodList.add(new CustomerServicePeriodMonth().setCustomerServiceId(customerServiceId)
                    .setPeriod(period)
                    .setBusinessDeptId(cCustomerService.getBusinessDeptId())
                    .setBusinessTopDeptId(cCustomerService.getBusinessTopDeptId())
                    .setAdvisorDeptId(cCustomerService.getAdvisorDeptId())
                    .setAdvisorTopDeptId(cCustomerService.getAdvisorTopDeptId())
                    .setServiceStatus(ServiceStatus.SERVICE.getCode())
                    .setAccountingDeptId(accountingDeptId)
                    .setAccountingTopDeptId(Long.parseLong(sysDept.getAncestors().split(",")[2]))
                    //.setAccountingTopDeptId(cCustomerService.getAccountingTopDeptId())
                    .setCustomerName(cCustomerService.getCustomerName())
                    .setTaxNumber(cCustomerService.getTaxNumber())
                    .setCreditCode(cCustomerService.getCreditCode())
                    .setTaxType(cCustomerService.getTaxType())
                    .setServiceNumber(cCustomerService.getServiceNumber())
                    .setBankPaymentResult(hasPeriodBank ? BankPaymentResult.WAIT_CREATE.getCode() : BankPaymentResult.UNOPENED.getCode())
                    .setInAccountStatus(AccountingCashierDeliverStatus.WAIT_CREATE.getCode())
                    .setSettleAccountStatus(InAccountStatus.UN_IN.getCode())
                    .setMedicalDeliverStatus(-2)
                    .setSocialDeliverStatus(-2)
                    .setPersonTaxDeliverStatus(-2)
                    .setOperationTaxDeliverStatus(-2)
                    .setNationalTaxDeliverStatus(-2)
                    .setPreAuthDeliverStatus(-2)
                    //从哪里生成的
                    .setServiceType(!Objects.isNull(addFromType) && Objects.equals(addFromType, CustomerServiceRepairAccountServiceImpl.ADD_FROM_TYPE) ? CustomerServicePeriodMonthServiceType.REPAIR_ACCOUNT.getCode() : CustomerServicePeriodMonthServiceType.DO_ACCOUNT.getCode())
                    .setAddFromType(addFromType)
                    .setYear(period / 100)
                    .setAddFromId(addFromId)
            );
            if (!yearPeriodList.contains(startMonth.getYear())) {
                yearPeriodList.add(startMonth.getYear());
            }
            startMonth = startMonth.plusMonths(1);
        }

        //生成账期
        //同时也会，生成入账交付单
        //CCustomerService customerService, List<CustomerServicePeriodMonth> monthPeriodList, List<CBusinessTagRelation> relations, List<CustomerServiceTaxTypeCheckVO> taxTypeCheckList
        saveMonthPeriodList(
                cCustomerService,
                monthPeriodList,
                tagDTOS,
                taxTypeCheckList,
                deptId,
                userId,
                operName
        );

        // 新增年度账期
        customerServicePeriodYearService.addCustomerServicePeriodYear(customerServiceId, yearPeriodList);
    }

    @Override
    public void updateAccountingByOperateAssign(Integer addFromType, Long addFromId, Long accountingDeptId) {
        //获取需要处理的账期
        List<CustomerServicePeriodMonth> needUpdates = list(new LambdaUpdateWrapper<CustomerServicePeriodMonth>()
                .eq(CustomerServicePeriodMonth::getAddFromType, addFromType)
                .eq(CustomerServicePeriodMonth::getAddFromId, addFromId)
        );

        if (!ObjectUtils.isEmpty(needUpdates)) {
            SysDept sysDept = remoteDeptService.getDeptInfo(accountingDeptId).getDataThrowException();

            //获取账期Id
            List<Long> periodIds = needUpdates.stream().map(CustomerServicePeriodMonth::getId).collect(Collectors.toList());

            //更新账期上的会计部门信息
            update(new LambdaUpdateWrapper<CustomerServicePeriodMonth>()
                    .in(CustomerServicePeriodMonth::getId, periodIds)
                    .set(CustomerServicePeriodMonth::getAccountingDeptId, accountingDeptId)
                    .set(CustomerServicePeriodMonth::getAccountingTopDeptId, Long.parseLong(sysDept.getAncestors().split(",")[2]))
            );

            //****** START 更新账期员工上的数据

            //先删除原来的会计
            customerServicePeriodEmployeeService.remove(
                    new LambdaQueryWrapper<CCustomerServicePeriodEmployee>()
                            .in(CCustomerServicePeriodEmployee::getPeriodId, periodIds)
                            .eq(CCustomerServicePeriodEmployee::getPeriodEmployeeType, 2)
            );

            //再新增会计
            List<SysEmployee> accountingEmployees = remoteEmployeeService.getEmployeeListByDeptId(accountingDeptId).getDataThrowException();
            if (!ObjectUtils.isEmpty(accountingEmployees)) {
                needUpdates.forEach(period ->
                        customerServicePeriodEmployeeService.saveBatch(
                                accountingEmployees.stream()
                                        .map(r -> new CCustomerServicePeriodEmployee()
                                                .setPeriodId(period.getId())
                                                .setPeriodEmployeeType(2)
                                                .setEmployeeId(r.getEmployeeId())
                                                .setEmployeeName(r.getEmployeeName()))
                                        .collect(Collectors.toList())));
            }
        }
    }

    @Override
    public void operateDeleteByAddFromType(Long deptId, Integer addFromType, Long addFromId) {
        remove(
                new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                        .eq(CustomerServicePeriodMonth::getAddFromType, addFromType)
                        .eq(CustomerServicePeriodMonth::getAddFromId, addFromId)
        );
    }

    @Override
    public List<CustomerServicePeriodMonth> periodSelectForAccountingCashier(Long customerServiceId, Integer accountingCashierType) {
        if (Objects.isNull(customerServiceId) || Objects.isNull(accountingCashierType)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<CustomerServicePeriodMonth> queryWrapper = new LambdaQueryWrapper<CustomerServicePeriodMonth>().eq(CustomerServicePeriodMonth::getCustomerServiceId, customerServiceId)
                .orderByDesc(CustomerServicePeriodMonth::getPeriod);
        if (Objects.equals(accountingCashierType, AccountingCashierType.INCOME.getCode())) {
            queryWrapper.notInSql(CustomerServicePeriodMonth::getId, "select distinct customer_service_period_month_id from c_customer_service_cashier_accounting where is_del = 0 and `type` = 1 and customer_service_id = " + customerServiceId);
        } else if (Objects.equals(accountingCashierType, AccountingCashierType.CHANGE.getCode())) {
            queryWrapper.notInSql(CustomerServicePeriodMonth::getId, "select distinct customer_service_period_month_id from c_customer_service_cashier_accounting where is_del = 0 and `type` = 3 and deliver_status != 2 and customer_service_id = " + customerServiceId);
        }
        List<CustomerServicePeriodMonth> list = list(queryWrapper);
        if (!ObjectUtils.isEmpty(list)) {
            Map<Long, List<TagDTO>> tagMap = businessTagRelationService.getTagsByBusinessTypeForList(list.stream().map(CustomerServicePeriodMonth::getId).collect(Collectors.toList()), TagBusinessType.CUSTOMER_SERVICE_MONTH_PERIOD);
            list.forEach(l -> l.setHasTicket(tagMap.getOrDefault(l.getId(), Lists.newArrayList()).stream().anyMatch(t -> Objects.equals(t.getId(), specialTagProperties.getPprz()))));
        }
        return list;
    }

    @Override
    public List<String> materialFilePeriodSelect(Long materialDeliverId) {
        if (Objects.isNull(materialDeliverId)) {
            return Collections.emptyList();
        }
        return materialDeliverFileInventoryService.list(new LambdaQueryWrapper<MaterialDeliverFileInventory>()
                .eq(MaterialDeliverFileInventory::getIsDel, false)
                .isNotNull(MaterialDeliverFileInventory::getErrorMsg)
                .ne(MaterialDeliverFileInventory::getErrorMsg, "")
                .eq(MaterialDeliverFileInventory::getMaterialDeliverId, materialDeliverId)
                .select(MaterialDeliverFileInventory::getErrorMsg))
                .stream().map(MaterialDeliverFileInventory::getErrorMsg).distinct().collect(Collectors.toList());
    }

    @Override
    public boolean checkCanCollectDebt(Long customerServiceId, Integer periodStart, Integer periodEnd) {
        List<CustomerServicePeriodMonth> periodMonthList = list(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .eq(CustomerServicePeriodMonth::getCustomerServiceId, customerServiceId)
                .le(!Objects.isNull(periodEnd), CustomerServicePeriodMonth::getPeriod, periodEnd)
                .ge(!Objects.isNull(periodStart), CustomerServicePeriodMonth::getPeriod, periodStart));
        if (ObjectUtils.isEmpty(periodMonthList)) {
            return false;
        }
        for (CustomerServicePeriodMonth periodMonth : periodMonthList) {
            if (checkPeriodMonthCanCollectDebt(periodMonth)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void checkCanCollectDebtV2(Long customerServiceId, Integer periodStart, Integer periodEnd) {
        List<CustomerServicePeriodMonth> periodMonthList = list(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .eq(CustomerServicePeriodMonth::getCustomerServiceId, customerServiceId)
                .le(!Objects.isNull(periodEnd), CustomerServicePeriodMonth::getPeriod, periodEnd)
                .ge(!Objects.isNull(periodStart), CustomerServicePeriodMonth::getPeriod, periodStart));
        if (ObjectUtils.isEmpty(periodMonthList)) {
            throw new ServiceException("没有账期需要催账");
        }
        // 只要有一条满足以下条件，就不允许催账
        for (CustomerServicePeriodMonth periodMonth : periodMonthList) {
            if (Lists.newArrayList(BankPaymentResult.WAIT_CREATE.getCode(), BankPaymentResult.BANK_PARTIAL_MISSING.getCode(), BankPaymentResult.EXCEPTION.getCode()).contains(periodMonth.getBankPaymentResult())
                    || Lists.newArrayList(AccountingCashierDeliverStatus.WAIT_CREATE.getCode(), AccountingCashierDeliverStatus.DELIVER_EXCEPTION.getCode(), AccountingCashierDeliverStatus.WAIT_RESUBMIT.getCode()).contains(periodMonth.getInAccountStatus())) {
                throw new ServiceException("当前催账账期范围内存在未交接，或异常未处理账期，或催账账期均已结账");
            }
        }
        // 只要有一条满足以下条件，就允许催账
        for (CustomerServicePeriodMonth periodMonth : periodMonthList) {
            if (checkPeriodMonthCanCollectDebt(periodMonth)) {
                return;
            }
        }
        throw new ServiceException("没有账期需要催账");
    }

    private boolean checkPeriodMonthCanCollectDebt(CustomerServicePeriodMonth periodMonth) {
        if (Lists.newArrayList(BankPaymentResult.UNOPENED.getCode()).contains(periodMonth.getBankPaymentResult()) && Lists.newArrayList(AccountingCashierDeliverStatus.WAIT_DELIVER.getCode(), AccountingCashierDeliverStatus.WAIT_SUBMIT.getCode()).contains(periodMonth.getInAccountStatus())) {
            return true;
        }
        if (Lists.newArrayList(BankPaymentResult.DELIVERY_IN_PROGRESS.getCode(), BankPaymentResult.NO_DELIVERY_REQUIRED.getCode(), BankPaymentResult.NO_FLOW.getCode(), BankPaymentResult.NORMAL_COMPLETION.getCode(), BankPaymentResult.COMPLETED.getCode()).contains(periodMonth.getBankPaymentResult()) && Lists.newArrayList(AccountingCashierDeliverStatus.WAIT_DELIVER.getCode(), AccountingCashierDeliverStatus.WAIT_SUBMIT.getCode()).contains(periodMonth.getInAccountStatus())) {
            return true;
        }
        if (Lists.newArrayList(BankPaymentResult.DELIVERY_IN_PROGRESS.getCode()).contains(periodMonth.getBankPaymentResult()) && Lists.newArrayList(AccountingCashierDeliverStatus.DELIVER_COMPLETE.getCode()).contains(periodMonth.getInAccountStatus())) {
            return true;
        }
        return false;
    }

    @Override
    public CustomerServicePeriodMonth selectByCustomerServiceIdAndPeriod(Long customerServiceId, Integer period) {
        return getOne(new LambdaQueryWrapper<CustomerServicePeriodMonth>()
                .eq(CustomerServicePeriodMonth::getCustomerServiceId, customerServiceId)
                .eq(CustomerServicePeriodMonth::getPeriod, period).last("limit 1"));
    }
}
